#!/bin/bash

# Comprehensive build verification script
set -e

echo "🔍 Starting comprehensive build verification..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ERRORS=0
WARNINGS=0

# Function to log errors
log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((ERRORS++))
}

# Function to log warnings
log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNINGS++))
}

# Function to log success
log_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

echo "🔍 Running pre-build checks..."

# Check required files exist
required_files=("package.json" "tsconfig.json" "src/index.ts" "src/server.ts")
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        log_success "$file exists"
    else
        log_error "Required file missing: $file"
    fi
done

# Check TypeScript configuration
if [[ -f "tsconfig.json" ]]; then
    if grep -q '"target": "ES2022"' tsconfig.json; then
        log_success "TypeScript target is ES2022"
    else
        log_warning "TypeScript target should be ES2022"
    fi

    if grep -q '"module": "Node16"' tsconfig.json; then
        log_success "TypeScript module is Node16"
    else
        log_warning "TypeScript module should be Node16"
    fi

    if grep -q '"strict": true' tsconfig.json; then
        log_success "TypeScript strict mode enabled"
    else
        log_warning "TypeScript strict mode should be enabled"
    fi
fi

# Check package.json structure
if [[ -f "package.json" ]]; then
    if grep -q '"type": "module"' package.json; then
        log_success "package.json type is module"
    else
        log_error "package.json type should be 'module'"
    fi

    if grep -q '"bin":' package.json; then
        log_success "package.json has bin field"
    else
        log_error "package.json missing bin field"
    fi
fi

echo ""
echo "🔨 Verifying TypeScript compilation..."

# Clean and build
npm run clean > /dev/null 2>&1
if npm run build > /dev/null 2>&1; then
    log_success "TypeScript compilation successful"
else
    log_error "TypeScript compilation failed"
fi

echo ""
echo "📦 Verifying module exports..."

# Check all index.ts files have proper exports (except main entry point)
find src -name "index.ts" -type f | while read -r file; do
    # Skip the main entry point
    if [[ "$file" == "src/index.ts" ]]; then
        log_success "$file is main entry point (no exports needed)"
    elif grep -q "export" "$file"; then
        log_success "$file has exports"
    else
        log_warning "$file has no exports"
    fi
done

echo ""
echo "🛠️  Verifying tool registration..."

# Check server.ts for tool registration calls
if [[ -f "src/server.ts" ]]; then
    registration_patterns=("registerProjectTools" "registerFileTools" "registerBuildTools" "registerXcodeTools" "registerConsolidatedXcodeTools" "registerXcodeDistributionTools")

    for pattern in "${registration_patterns[@]}"; do
        if grep -q "$pattern" src/server.ts; then
            log_success "$pattern found"
        else
            log_warning "Tool registration function $pattern not found in server.ts"
        fi
    done
fi

echo ""
echo "📚 Verifying dependencies..."

# Run npm audit
if npm audit --audit-level=moderate > /dev/null 2>&1; then
    log_success "No security vulnerabilities found"
else
    log_warning "npm audit found potential issues"
fi

echo ""
echo "🚀 Verifying runtime startup..."

# Test if the server can start (with timeout using gtimeout or built-in method)
if command -v gtimeout > /dev/null 2>&1; then
    # Use gtimeout on macOS (if installed via brew install coreutils)
    if gtimeout 5s node dist/index.js > /dev/null 2>&1; then
        log_success "Server starts successfully"
    elif [[ $? -eq 124 ]]; then
        log_success "Server starts successfully (timeout expected)"
    else
        log_error "Server failed to start"
    fi
else
    # Use a background process with kill for timeout
    node dist/index.js > /dev/null 2>&1 &
    SERVER_PID=$!
    sleep 3
    if kill -0 $SERVER_PID 2>/dev/null; then
        log_success "Server starts successfully"
        kill $SERVER_PID 2>/dev/null
    else
        log_error "Server failed to start"
    fi
fi

echo ""
echo "📊 Build Verification Summary"
echo "=============================="
echo -e "Errors: ${RED}$ERRORS${NC}"
echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"

if [[ $ERRORS -eq 0 ]]; then
    echo -e "${GREEN}✅ Build verification PASSED${NC}"
    echo ""
    echo "🎉 All verification steps passed! The project is ready for deployment."

    if [[ $WARNINGS -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Consider addressing the warnings to improve code quality.${NC}"
    fi

    exit 0
else
    echo -e "${RED}❌ Build verification FAILED${NC}"
    echo ""
    echo "🔧 Please fix the errors listed above before proceeding with deployment."
    exit 1
fi
