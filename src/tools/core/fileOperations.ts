import { z } from "zod";
import { FileToolBase } from "../../utils/toolBase.js";
import { ToolResult } from "../../utils/toolFactory.js";
import { XcodeServer } from "../../server.js";
import { FileHelpers } from "../../utils/fileHelpers.js";
import { ToolCategory, ToolRegistry } from "../categories.js";

/**
 * File read tool with intelligent caching and optimization
 */
export class ReadFileTool extends FileToolBase<{
  filePath: string;
  encoding?: string;
  asBinary?: boolean;
  maxSize?: number;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "read_file",
      "Read file contents with error handling and performance monitoring",
      z.object({
        filePath: z.string().describe("Path to the file to read"),
        encoding: z
          .string()
          .optional()
          .describe("File encoding (default: utf-8)"),
        asBinary: z
          .boolean()
          .optional()
          .describe("Read as binary and return base64"),
        maxSize: z.number().optional().describe("Maximum file size in bytes"),
      })
    );

    // Register with tool registry
    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "read", "advanced", "optimized"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    filePath: string;
    encoding?: string;
    asBinary?: boolean;
    maxSize?: number;
  }): Promise<ToolResult> {
    try {
      // Validate and resolve path
      const resolvedPath = await this.validateAndResolvePath(
        params.filePath,
        "read"
      );

      // Check if file exists
      if (!(await this.fileExists(resolvedPath))) {
        return this.createErrorResponse(
          `File does not exist: ${params.filePath}`
        );
      }

      // Read file using enhanced helper
      const result = await FileHelpers.readFile(resolvedPath, {
        encoding: params.encoding as any,
        asBinary: params.asBinary,
        maxSize: params.maxSize,
      });

      return this.createSuccessResponse(
        `Successfully read file: ${params.filePath}`,
        {
          content: result.content,
          size: result.size,
          isBinary: result.isBinary,
          path: resolvedPath,
          encoding: params.encoding || "utf-8",
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        `Failed to read file: ${params.filePath}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}

/**
 * File write tool with intelligent backup and optimization
 */
export class WriteFileTool extends FileToolBase<{
  filePath: string;
  content: string;
  encoding?: string;
  fromBase64?: boolean;
  createPath?: boolean;
  backup?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "write_file",
      "Write file contents with error handling and backup support",
      z.object({
        filePath: z.string().describe("Path to the file to write"),
        content: z.string().describe("Content to write to the file"),
        encoding: z
          .string()
          .optional()
          .describe("File encoding (default: utf-8)"),
        fromBase64: z
          .boolean()
          .optional()
          .describe("Decode content from base64"),
        createPath: z
          .boolean()
          .optional()
          .describe("Create directory path if needed"),
        backup: z.boolean().optional().describe("Create backup before writing"),
      })
    );

    // Register with tool registry
    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "write", "advanced", "backup", "optimized"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    filePath: string;
    content: string;
    encoding?: string;
    fromBase64?: boolean;
    createPath?: boolean;
    backup?: boolean;
  }): Promise<ToolResult> {
    try {
      // Validate and resolve path
      const resolvedPath = await this.validateAndResolvePath(
        params.filePath,
        "write"
      );

      // Write file using enhanced helper
      await FileHelpers.writeFile(resolvedPath, params.content, {
        encoding: params.encoding as any,
        fromBase64: params.fromBase64,
        createPath: params.createPath,
        backup: params.backup,
      });

      return this.createSuccessResponse(
        `Successfully wrote file: ${params.filePath}`,
        {
          path: resolvedPath,
          size: params.content.length,
          encoding: params.encoding || "utf-8",
          backup: params.backup || false,
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        `Failed to write file: ${params.filePath}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}

/**
 * File search tool with intelligent indexing and optimization
 */
export class SearchFilesTool extends FileToolBase<{
  searchPath: string;
  searchText: string;
  filePattern: string;
  isRegex?: boolean;
  caseSensitive?: boolean;
  maxResults?: number;
  includeHidden?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "search_in_files",
      "Search for text within files with filtering and performance optimization",
      z.object({
        searchPath: z.string().describe("Directory to search in"),
        searchText: z.string().describe("Text or regex pattern to search for"),
        filePattern: z
          .string()
          .describe("File pattern to match (e.g., '*.swift')"),
        isRegex: z.boolean().optional().describe("Treat searchText as regex"),
        caseSensitive: z.boolean().optional().describe("Case sensitive search"),
        maxResults: z.number().optional().describe("Maximum number of results"),
        includeHidden: z.boolean().optional().describe("Include hidden files"),
      })
    );

    // Register with tool registry
    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "search", "regex", "advanced", "optimized"],
      complexity: "intermediate",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    searchPath: string;
    searchText: string;
    filePattern: string;
    isRegex?: boolean;
    caseSensitive?: boolean;
    maxResults?: number;
    includeHidden?: boolean;
  }): Promise<ToolResult> {
    try {
      // Validate and resolve path
      const resolvedPath = await this.validateAndResolvePath(
        params.searchPath,
        "read"
      );

      // Check if search path exists and is a directory
      if (!(await FileHelpers.isDirectory(resolvedPath))) {
        return this.createErrorResponse(
          `Search path is not a directory: ${params.searchPath}`
        );
      }

      // Search using enhanced helper
      const results = await FileHelpers.searchInFiles(
        resolvedPath,
        params.searchText,
        params.filePattern,
        {
          isRegex: params.isRegex,
          caseSensitive: params.caseSensitive,
          maxResults: params.maxResults,
          includeHidden: params.includeHidden,
        }
      );

      const totalMatches = results.reduce(
        (sum, file) => sum + file.matches.length,
        0
      );

      return this.createSuccessResponse(
        `Found ${totalMatches} matches in ${results.length} files`,
        {
          searchPath: resolvedPath,
          searchText: params.searchText,
          filePattern: params.filePattern,
          totalFiles: results.length,
          totalMatches,
          results: results.map((file) => ({
            file: file.file,
            matchCount: file.matches.length,
            matches: file.matches.slice(0, 10), // Limit matches per file for performance
          })),
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        `Failed to search files in: ${params.searchPath}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}

/**
 * Register file tools
 */
export function registerFileTools(server: XcodeServer): void {
  const readTool = new ReadFileTool(server);
  const writeTool = new WriteFileTool(server);
  const searchTool = new SearchFilesTool(server);

  readTool.register();
  writeTool.register();
  searchTool.register();
}

/**
 * @deprecated Use registerFileTools instead
 */
export function registerAdvancedFileTools(server: XcodeServer): void {
  registerFileTools(server);
}

/**
 * @deprecated Use registerFileTools instead
 */
export function registerEnterpriseFileTools(server: XcodeServer): void {
  registerFileTools(server);
}
