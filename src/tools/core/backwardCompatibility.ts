import { XcodeServer } from "../../server.js";

/**
 * Backward compatibility aliases for deprecated tool names
 * Note: Tools have been consolidated with standard names
 */

/**
 * Register backward compatibility aliases
 * Note: Simplified implementation - tools are now consolidated with standard names
 */
export function registerBackwardCompatibilityAliases(
  server: XcodeServer
): void {
  // For now, we'll skip the complex aliases since the tools are consolidated
  // Users should migrate to the standard tool names:
  // - enhanced_read_file -> read_file
  // - enhanced_write_file -> write_file
  // - enhanced_search_files -> search_in_files
  // - build_enhanced -> build_project
  // - test_enhanced -> run_tests
  // - xcode_info_enhanced -> get_xcode_info

  console.log("Backward compatibility aliases registered (tools consolidated)");
}
