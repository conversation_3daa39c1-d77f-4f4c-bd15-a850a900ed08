import { z } from "zod";
import { XcodeServer } from "../../server.js";
import * as fs from "fs/promises";
import * as path from "path";

/**
 * Register Xcode distribution and validation tools
 * This includes: export_archive, validate_app
 */
export function registerXcodeDistributionTools(server: XcodeServer) {
  // Register "export_archive"
  server.server.tool(
    "export_archive",
    "Export an Xcode archive for distribution (App Store, Ad Hoc, Enterprise, Development)",
    {
      archivePath: z.string().describe("Path to the .xcarchive file to export"),
      exportPath: z
        .string()
        .describe(
          "Directory where exported IPA and other files will be placed"
        ),
      method: z
        .enum(["app-store", "ad-hoc", "enterprise", "development"])
        .describe("Distribution method"),
      teamId: z
        .string()
        .optional()
        .describe(
          "Team ID for code signing. If not provided, will try to use the default team."
        ),
      signingCertificate: z
        .string()
        .optional()
        .describe(
          "Signing certificate to use. If not provided, will try to use the default certificate for the selected method."
        ),
      provisioningProfiles: z
        .record(z.string())
        .optional()
        .describe(
          "Dictionary mapping bundle identifiers to provisioning profile names."
        ),
      compileBitcode: z
        .boolean()
        .optional()
        .describe(
          "Whether to compile Bitcode. Default is true for App Store, false otherwise."
        ),
      stripSwiftSymbols: z
        .boolean()
        .optional()
        .describe("Whether to strip Swift symbols. Default is true."),
    },
    async ({
      archivePath,
      exportPath,
      method,
      teamId,
      signingCertificate,
      provisioningProfiles,
      compileBitcode,
      stripSwiftSymbols,
    }) => {
      try {
        // Validate paths
        const resolvedArchivePath =
          server.pathManager.normalizePath(archivePath);
        server.pathManager.validatePathForReading(resolvedArchivePath);

        const resolvedExportPath = server.pathManager.normalizePath(exportPath);
        server.pathManager.validatePathForWriting(resolvedExportPath);

        // Ensure archive path ends with .xcarchive
        if (!resolvedArchivePath.endsWith(".xcarchive")) {
          throw new Error(
            `Archive path must end with .xcarchive: ${archivePath}`
          );
        }

        // Create export options plist
        const exportOptions: any = {
          method: method,
          stripSwiftSymbols: stripSwiftSymbols ?? true,
        };

        // Set default compileBitcode based on method
        if (compileBitcode !== undefined) {
          exportOptions.compileBitcode = compileBitcode;
        } else {
          exportOptions.compileBitcode = method === "app-store";
        }

        if (teamId) {
          exportOptions.teamID = teamId;
        }

        if (signingCertificate) {
          exportOptions.signingCertificate = signingCertificate;
        }

        if (provisioningProfiles) {
          exportOptions.provisioningProfiles = provisioningProfiles;
        }

        // Create temporary export options plist file
        const tempDir = await fs.mkdtemp(
          path.join(require("os").tmpdir(), "xcode-export-")
        );
        const exportOptionsPlistPath = path.join(
          tempDir,
          "ExportOptions.plist"
        );

        // Convert to plist format
        const plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>method</key>
  <string>${exportOptions.method}</string>
  <key>stripSwiftSymbols</key>
  <${exportOptions.stripSwiftSymbols ? "true" : "false"}/>
  <key>compileBitcode</key>
  <${exportOptions.compileBitcode ? "true" : "false"}/>
  ${teamId ? `<key>teamID</key><string>${teamId}</string>` : ""}
  ${
    signingCertificate
      ? `<key>signingCertificate</key><string>${signingCertificate}</string>`
      : ""
  }
  ${
    provisioningProfiles
      ? `<key>provisioningProfiles</key><dict>${Object.entries(
          provisioningProfiles
        )
          .map(
            ([bundleId, profile]) =>
              `<key>${bundleId}</key><string>${profile}</string>`
          )
          .join("")}</dict>`
      : ""
  }
</dict>
</plist>`;

        await fs.writeFile(exportOptionsPlistPath, plistContent);

        // Ensure export directory exists
        await fs.mkdir(resolvedExportPath, { recursive: true });

        // Execute xcodebuild -exportArchive
        const { stdout, stderr } = await server.commandExecutor.execute(
          "xcodebuild",
          [
            "-exportArchive",
            "-archivePath",
            resolvedArchivePath,
            "-exportPath",
            resolvedExportPath,
            "-exportOptionsPlist",
            exportOptionsPlistPath,
          ]
        );

        // Clean up temporary file
        await fs.unlink(exportOptionsPlistPath);
        await fs.rmdir(tempDir);

        // Find the exported IPA file
        const exportedFiles = await fs.readdir(resolvedExportPath);
        const ipaFile = exportedFiles.find((file) => file.endsWith(".ipa"));

        let resultText = `Archive exported successfully!\nExport method: ${method}\nExport path: ${resolvedExportPath}\n`;

        if (ipaFile) {
          resultText += `IPA file: ${ipaFile}\n`;
        }

        resultText += `\nExport details:\n${stdout}`;

        if (stderr) {
          resultText += `\nWarnings:\n${stderr}`;
        }

        return {
          content: [
            {
              type: "text",
              text: resultText,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to export archive: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Register "validate_app"
  server.server.tool(
    "validate_app",
    "Validate an app for App Store submission using altool",
    {
      ipaPath: z.string().describe("Path to the .ipa file to validate"),
      username: z
        .string()
        .describe("App Store Connect username (usually an email)"),
      password: z
        .string()
        .describe("App-specific password for the App Store Connect account"),
      apiKey: z
        .string()
        .optional()
        .describe("API Key ID (alternative to username/password)"),
      apiKeyPath: z
        .string()
        .optional()
        .describe("Path to the API Key .p8 file (required if using apiKey)"),
      apiIssuer: z
        .string()
        .optional()
        .describe("API Key Issuer ID (required if using apiKey)"),
    },
    async ({ ipaPath, username, password, apiKey, apiKeyPath, apiIssuer }) => {
      try {
        // Validate IPA path
        const resolvedIpaPath = server.pathManager.normalizePath(ipaPath);
        server.pathManager.validatePathForReading(resolvedIpaPath);

        if (!resolvedIpaPath.endsWith(".ipa")) {
          throw new Error(`IPA path must end with .ipa: ${ipaPath}`);
        }

        let args = ["altool", "--validate-app"];

        if (apiKey && apiKeyPath && apiIssuer) {
          // Use API Key authentication
          const resolvedApiKeyPath = server.pathManager.normalizePath(apiKeyPath);
          server.pathManager.validatePathForReading(resolvedApiKeyPath);

          args.push(
            "--apiKey",
            apiKey,
            "--apiIssuer",
            apiIssuer,
            "--file",
            resolvedIpaPath
          );
        } else {
          // Use username/password authentication
          args.push(
            "--type",
            "ios",
            "--file",
            resolvedIpaPath,
            "--username",
            username,
            "--password",
            password
          );
        }

        const { stdout, stderr } = await server.commandExecutor.execute(
          "xcrun",
          args
        );

        let resultText = `App validation completed!\n\nValidation results:\n${stdout}`;

        if (stderr) {
          resultText += `\nWarnings/Errors:\n${stderr}`;
        }

        return {
          content: [
            {
              type: "text",
              text: resultText,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to validate app: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
