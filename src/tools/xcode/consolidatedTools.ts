import { z } from "zod";
import { XcodeServer } from "../../server.js";
import { SecureCommandExecutor } from "../../utils/commandExecutor.js";
import * as path from "path";

/**
 * Register non-duplicated Xcode tools (tools that don't have enterprise equivalents)
 * This includes: run_xcrun, run_lldb, trace_app, switch_xcode, export_archive, validate_app
 */
export function registerConsolidatedXcodeTools(server: XcodeServer) {
  // Register "run_xcrun"
  server.server.tool(
    "run_xcrun",
    "Executes a specified Xcode tool via xcrun",
    {
      tool: z.string().describe("The name of the Xcode tool to run"),
      args: z.string().optional().describe("Arguments to pass to the tool"),
      workingDir: z
        .string()
        .optional()
        .describe("Working directory to execute the command in"),
    },
    async ({ tool, args = "", workingDir }) => {
      try {
        let options = {};

        if (workingDir) {
          // Validate and resolve the working directory path
          const resolvedWorkingDir =
            server.pathManager.normalizePath(workingDir);
          server.pathManager.validatePathForReading(resolvedWorkingDir);
          options = { cwd: resolvedWorkingDir };
        }

        const result = await server.commandExecutor.execute("sh", [
          "-c",
          `xcrun ${tool} ${args}`,
        ]);
        const { stdout, stderr } = result;

        return {
          content: [
            {
              type: "text",
              text: `${tool} output:\n${stdout}\n${
                stderr ? "Error output:\n" + stderr : ""
              }`,
            },
          ],
        };
      } catch (error) {
        let stderr = "";
        if (error instanceof Error && "stderr" in error) {
          stderr = (error as any).stderr;
        }

        throw new Error(
          `Failed to execute xcrun ${tool}: ${
            error instanceof Error ? error.message : String(error)
          }${stderr ? "\nStderr: " + stderr : ""}`
        );
      }
    }
  );

  // Register "run_lldb"
  server.server.tool(
    "run_lldb",
    "Launches the LLDB debugger with optional arguments",
    {
      args: z.string().optional().describe("Arguments to pass to lldb"),
      command: z.string().optional().describe("Single LLDB command to execute"),
    },
    async ({ args = "", command }) => {
      try {
        let lldbArgs = ["lldb"];

        if (command) {
          // Execute a single command and exit
          lldbArgs.push("-o", command, "-o", "quit");
        } else if (args) {
          // Add custom arguments
          lldbArgs = lldbArgs.concat(args.split(" ").filter(Boolean));
        }

        const { stdout, stderr } = await server.commandExecutor.execute(
          "xcrun",
          lldbArgs
        );

        return {
          content: [
            {
              type: "text",
              text: `LLDB output:\n${stdout}\n${
                stderr ? "Error output:\n" + stderr : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to launch LLDB: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Register "trace_app"
  server.server.tool(
    "trace_app",
    "Captures a performance trace of an application using xctrace",
    {
      appPath: z.string().describe("Path to the application to trace"),
      duration: z
        .number()
        .optional()
        .describe("Duration of the trace in seconds (default: 10)"),
      template: z
        .string()
        .optional()
        .describe("Trace template to use (default: 'Time Profiler')"),
      outputPath: z
        .string()
        .optional()
        .describe(
          "Path where to save the trace file (default: app_trace.trace in active directory)"
        ),
      startSuspended: z
        .boolean()
        .optional()
        .describe("Start the application in a suspended state"),
    },
    async ({
      appPath,
      duration = 10,
      template = "Time Profiler",
      outputPath,
      startSuspended = false,
    }) => {
      try {
        // Validate app path
        const resolvedAppPath = server.pathManager.normalizePath(appPath);
        server.pathManager.validatePathForReading(resolvedAppPath);

        // Determine output path
        const activeDir = server.directoryState.getActiveDirectory();
        const traceOutputPath =
          outputPath || path.join(activeDir, `app_trace_${Date.now()}.trace`);

        // Validate output directory
        const outputDir = path.dirname(traceOutputPath);
        server.pathManager.validatePathForWriting(outputDir);

        // Build xctrace command
        const args = [
          "xctrace",
          "record",
          "--template",
          template,
          "--time-limit",
          `${duration}s`,
          "--output",
          traceOutputPath,
        ];

        if (startSuspended) {
          args.push("--launch-suspended");
        } else {
          args.push("--launch");
        }

        args.push(resolvedAppPath);

        const { stdout, stderr } = await server.commandExecutor.execute(
          "xcrun",
          args
        );

        return {
          content: [
            {
              type: "text",
              text: `Performance trace completed.\nTrace saved to: ${traceOutputPath}\nDuration: ${duration} seconds\nTemplate: ${template}\n\nOutput:\n${stdout}\n${
                stderr ? "Warnings:\n" + stderr : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to trace application: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Register "switch_xcode"
  server.server.tool(
    "switch_xcode",
    "Switch the active Xcode version",
    {
      xcodePath: z
        .string()
        .optional()
        .describe(
          "Path to the Xcode.app to use. If not provided, available Xcode installations will be listed."
        ),
      version: z
        .string()
        .optional()
        .describe(
          "Version of Xcode to use (e.g., '14.3'). Will use the first matching version found."
        ),
    },
    async ({ xcodePath, version }) => {
      try {
        if (!xcodePath && !version) {
          // List available Xcode installations
          const { stdout } = await SecureCommandExecutor.execute("sh", [
            "-c",
            "find /Applications -name 'Xcode*.app' -maxdepth 2 2>/dev/null",
          ]);

          const paths = stdout.trim().split("\n").filter(Boolean);
          let formattedOutput = "Available Xcode installations:\n\n";

          for (const path of paths) {
            try {
              const { stdout: versionOutput } =
                await SecureCommandExecutor.execute("sh", [
                  "-c",
                  `/usr/libexec/PlistBuddy -c "Print :CFBundleShortVersionString" "${path}/Contents/Info.plist"`,
                ]);
              formattedOutput += `${path} (Version: ${versionOutput.trim()})\n`;
            } catch {
              formattedOutput += `${path} (Version: Unknown)\n`;
            }
          }

          formattedOutput +=
            "\nUse the 'xcodePath' or 'version' parameter to switch to a specific Xcode installation.";

          return {
            content: [
              {
                type: "text",
                text: formattedOutput,
              },
            ],
          };
        }

        let targetPath = xcodePath;

        if (version && !xcodePath) {
          // Find Xcode by version
          const { stdout } = await SecureCommandExecutor.execute("sh", [
            "-c",
            "find /Applications -name 'Xcode*.app' -maxdepth 2 2>/dev/null",
          ]);

          const paths = stdout.trim().split("\n").filter(Boolean);

          for (const path of paths) {
            try {
              const { stdout: versionOutput } =
                await SecureCommandExecutor.execute("sh", [
                  "-c",
                  `/usr/libexec/PlistBuddy -c "Print :CFBundleShortVersionString" "${path}/Contents/Info.plist"`,
                ]);

              if (versionOutput.trim().startsWith(version)) {
                targetPath = path;
                break;
              }
            } catch {
              // Skip if we can't get version info
            }
          }

          if (!targetPath) {
            throw new Error(
              `No Xcode installation found with version ${version}`
            );
          }
        }

        if (!targetPath) {
          throw new Error("No Xcode path specified");
        }

        // Validate the Xcode path
        const resolvedPath = server.pathManager.normalizePath(targetPath);
        server.pathManager.validatePathForReading(resolvedPath);

        if (!resolvedPath.endsWith(".app")) {
          throw new Error("Xcode path must point to an .app bundle");
        }

        // Switch to the specified Xcode
        const developerPath = `${resolvedPath}/Contents/Developer`;
        await server.commandExecutor.execute("sudo", [
          "xcode-select",
          "-s",
          developerPath,
        ]);

        // Verify the switch
        const { stdout: newPath } = await server.commandExecutor.execute(
          "xcode-select",
          ["-p"]
        );

        return {
          content: [
            {
              type: "text",
              text: `Successfully switched to Xcode at: ${resolvedPath}\nActive developer path: ${newPath.trim()}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to switch Xcode: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
